const CACHE_NAME = 'pokecatalog-v3'; // Cambia versione per forzare aggiornamento
const urlsToCache = [
  '/',
  '/manifest.json',
  '/img/pokeball.png'
];

self.addEventListener('install', event => {
  // Forza l'attivazione immediata del nuovo SW
  self.skipWaiting();
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('activate', event => {
  // Rimuovi cache vecchie
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      // Prendi controllo di tutte le pagine aperte
      return self.clients.claim();
    })
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    // Network first per HTML, cache first per assets
    fetch(event.request)
      .then(response => {
        const responseClone = response.clone();
        caches.open(CACHE_NAME)
          .then(cache => cache.put(event.request, responseClone));
        return response;
      })
      .catch(() => caches.match(event.request))
  );
});

