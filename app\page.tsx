'use client';

import { useState, useCallback } from 'react';
import PokemonSearch from '@/components/PokemonSearch';
import PokemonCard from '@/components/PokemonCard';

interface Pokemon {
  id: number;
  name: string;
  types: Array<{
    slot: number;
    type: {
      name: string;
      url: string;
    };
  }>;
  sprites: {
    front_default: string;
  };
}

export default function Home() {
  const [searchResults, setSearchResults] = useState<Pokemon[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearchResults = useCallback((results: Pokemon[]) => {
    setSearchResults(results);
    setHasSearched(true);
  }, []);

  const handleLoadingChange = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  return (
    <div className="min-h-screen bg-soft-white">

      {/* Hero Section */}
      <div className="bg-gradient-to-br from-poke-blue via-accent-sky to-electric-yellow py-10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h1 className="font-title text-5xl md:text-6xl font-bold text-battle-red mb-6 drop-shadow-lg">
            🔍 Pokédex
          </h1>
          <p className="font-body text-xl md:text-2xl text-battle-red/90 mb-8 drop-shadow-md">
            Cerca e scopri tutti i tuoi Pokemon preferiti
          </p>
        </div>
      </div>


      
       {/* Search Section */}
     
      <div className="py-2">
        <div className="max-w-6xl mx-auto px-4">
          <PokemonSearch 
            onSearchResults={handleSearchResults}
            onLoadingChange={handleLoadingChange}
          />

          <div className="mt-8">
            {isLoading && (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-poke-blue"></div>
              </div>
            )}

            {!isLoading && searchResults.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {searchResults.map((pokemon) => (
                  <PokemonCard key={pokemon.id} pokemon={pokemon} />
                ))}
              </div>
            )}

            {!isLoading && hasSearched && searchResults.length === 0 && (
              <div className="text-center py-12">
                <div className="mb-4">
                  <svg
                    className="w-16 h-16 mx-auto text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.137 0-4.146-.832-5.657-2.343l-1.1 1.1c-.331.331-.331.867 0 1.198l3.149 3.149c.331.331.867.331 1.198 0l1.1-1.1A7.963 7.963 0 0012 21c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8a7.963 7.963 0 004.343 7.157l1.1-1.1c-.331-.331-.331-.867 0-1.198l3.149-3.149z"
                    />
                  </svg>
                </div>
                <p className="text-gray-600 text-lg font-body">
                  Nessun Pokemon trovato
                </p>
                <p className="text-gray-400 text-sm mt-2 font-body">
                  Prova con un altro nome o controlla l'ortografia
                </p>
              </div>
            )}

            {!isLoading && !hasSearched && (
              <div className="text-center py-12">
                <div className="mb-4">
                  <svg
                    className="w-16 h-16 mx-auto text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <p className="text-gray-600 text-lg font-body">
                  Inizia a digitare per cercare un Pokemon...
                </p>
                <p className="text-gray-400 text-sm mt-2 font-body">
                  Prova con "pikachu", "charizard", o qualsiasi altro Pokemon!
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
