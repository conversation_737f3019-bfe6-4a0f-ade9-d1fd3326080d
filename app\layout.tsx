import type { Metadata } from "next";
import "./globals.css";
import { LanguageProvider } from "@/contexts/LanguageContext";
import TopBar from "@/components/TopBar";
import UpdateNotification from '@/components/UpdateNotification';

export const metadata: Metadata = {
  title: "PokéCatalog - Il tuo Pokédex completo",
  description: "Cerca e scopri tutti i tuoi Pokemon preferiti con informazioni dettagliate",
  manifest: "/manifest.json",
  viewport: "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",
  icons: {
    icon: '/img/pokeball.png',
    shortcut: '/img/pokeball.png',
    apple: '/img/pokeball.png',
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "PokéCatalog"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link 
          href="https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700;800&display=swap" 
          rel="stylesheet" 
        />
        <link rel="icon" href="/img/pokeball.png" type="image/png" />
        <link rel="shortcut icon" href="/img/pokeball.png" type="image/png" />
        <link rel="apple-touch-icon" href="/img/pokeball.png" />
        <script dangerouslySetInnerHTML={{
          __html: `
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js');
              });
            }
          `
        }} />
      </head>
      <body className="antialiased font-body bg-soft-white text-dark-grey min-h-screen">
        <LanguageProvider>
          <TopBar />
          <main className="min-h-screen">
            {children}
          </main>
          <UpdateNotification />
        </LanguageProvider>
      </body>
    </html>
  );
}



