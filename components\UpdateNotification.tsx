'use client';

import { useState, useEffect } from 'react';

export default function UpdateNotification() {
  const [showUpdate, setShowUpdate] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(reg => {
          setRegistration(reg);
          
          // Controlla aggiornamenti ogni 30 secondi
          setInterval(() => {
            reg.update();
          }, 30000);

          reg.addEventListener('updatefound', () => {
            const newWorker = reg.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setShowUpdate(true);
                }
              });
            }
          });
        });
    }
  }, []);

  const handleUpdate = () => {
    if (registration?.waiting) {
      registration.waiting.postMessage({ action: 'skipWaiting' });
      window.location.reload();
    }
  };

  if (!showUpdate) return null;

  return (
    <div className="fixed bottom-4 left-4 right-4 bg-poke-blue text-white p-4 rounded-lg shadow-lg z-50">
      <div className="flex items-center justify-between">
        <div>
          <p className="font-semibold">Aggiornamento disponibile!</p>
          <p className="text-sm opacity-90">Nuova versione dell'app pronta</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setShowUpdate(false)}
            className="px-3 py-1 text-sm bg-white/20 rounded"
          >
            Dopo
          </button>
          <button
            onClick={handleUpdate}
            className="px-3 py-1 text-sm bg-electric-yellow text-dark-grey rounded font-semibold"
          >
            Aggiorna
          </button>
        </div>
      </div>
    </div>
  );
}