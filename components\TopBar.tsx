'use client';

import Image from 'next/image';
import Link from 'next/link';
import LanguageSelector from './LanguageSelector';

export default function TopBar() {
  return (
    <div className="bg-poke-blue shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo e Titolo */}
          <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
            <div className="relative w-10 h-10">
              <Image
                src="/img/pokeball.png"
                alt="Pokemon Logo"
                width={40}
                height={40}
                className="pixelated"
                style={{ imageRendering: 'pixelated' }}
              />
            </div>
            <h1 className="font-title text-2xl font-bold text-soft-white">
              PokéCatalog
            </h1>
          </Link>

          {/* Pulsanti a destra */}
          <div className="flex items-center gap-3">
            <LanguageSelector />
          </div>
        </div>
      </div>
    </div>
  );
}
